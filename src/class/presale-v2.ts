import { Abi, Address, Chain, Client, getContract, WalletClient } from 'viem';

import { membershipV2Abi } from '@/abi/membership/membership-v2-abi';
import { presaleV2Abi } from '@/abi/presale/presale-v2-abi';

import {
  IPresale,
  Membership,
  Presale,
  // PresaleRoundState,
  PresaleRoundStateValue,
  Round,
} from './interface/presale';

type GetRoundsReturnType = [number[], Round[], PresaleRoundStateValue[]];

interface PresaleV2Props {
  client: Client;
  contractAddress: Address;
  presaleAbi: Abi;
  vestedToken: Address;
  collectedToken: Address;
  listingTimestamp: bigint;
  claimbackPeriod: bigint;
  rounds: Round[];
  membership: Address;
}

// @ts-expect-error TODO
export class PresaleV2 implements IPresale {
  public getVersion(): 'v2' {
    return 'v2';
  }

  public client: Client;

  public presaleContractAddress: Address;

  public presaleAbi: Abi;

  /// ERC20 implementation of the token sold.
  public vestedToken: Address;

  /// ERC20 implementation of the token collected.
  public collectedToken: Address;

  /// Timestamp indicating when the tge should be available.
  public listingTimestamp: bigint;

  /// How much time in seconds since `listingTimestamp` do Users have to claimback collectedToken
  public claimbackPeriod: bigint;

  /// Presale rounds settings
  public rounds: Round[];

  public membership: Address;

  constructor({
    client,
    contractAddress,
    presaleAbi,
    vestedToken,
    collectedToken,
    listingTimestamp,
    claimbackPeriod,
    rounds,
    membership,
  }: PresaleV2Props) {
    this.client = client;

    this.presaleContractAddress = contractAddress;
    this.presaleAbi = presaleAbi;

    this.vestedToken = vestedToken;
    this.collectedToken = collectedToken;
    this.listingTimestamp = listingTimestamp;
    this.claimbackPeriod = claimbackPeriod;
    this.rounds = rounds;

    this.membership = membership;
  }

  public static async createInstance(
    publicClient: Client,
    presaleContractAddress: Address,
  ): Promise<IPresale> {
    const presaleContract = getContract({
      client: publicClient,
      address: presaleContractAddress,
      abi: presaleV2Abi,
    });

    const [
      vestedToken,
      collectedToken,
      rawRoundsData,
      membership,
      listingTimestamp,
      claimbackPeriod,
    ] = await Promise.all([
      presaleContract.read.tokenA() as Promise<Address>,
      presaleContract.read.tokenB() as Promise<Address>,
      presaleContract.read.getRounds() as unknown as Promise<GetRoundsReturnType>,
      presaleContract.read.membership() as Promise<Address>,
      presaleContract.read.listingTimestamp() as Promise<bigint>,
      presaleContract.read.claimbackPeriod() as Promise<bigint>,
    ]);

    const [ids, data, states] = rawRoundsData;

    // for each id in ids, create a round object
    const rounds: Round[] = ids.map((id, index) => {
      const round = data[index];
      const state = states[index];

      return {
        roundId: Number(id),
        state,
        name: round.name,
        startTimestamp: Number(round.startTimestamp) * 1000,
        endTimestamp: Number(round.endTimestamp) * 1000,
        listingTimestamp: Number(listingTimestamp) * 1000,
        refundsEndTimestamp:
          listingTimestamp > 0n ? Number(listingTimestamp + claimbackPeriod) * 1000 : 0,
        proofsUri: round.proofsUri,
        whitelistRoot: round.whitelistRoot,
      };
    });

    // @ts-expect-error TODO
    return new PresaleV2({
      client: publicClient,
      contractAddress: presaleContractAddress,
      presaleAbi: presaleV2Abi,
      vestedToken,
      collectedToken,
      listingTimestamp,
      claimbackPeriod,
      rounds,
      membership,
    });
  }

  public getPresaleData(): Presale {
    return {
      presaleContractAddress: this.presaleContractAddress,
      vestedToken: this.vestedToken,
      collectedToken: this.collectedToken,
      listingTimestamp: Number(this.listingTimestamp),
      claimbackPeriod: Number(this.claimbackPeriod),
      rounds: this.rounds,
    };
  }

  public async getMemberships(walletAddress: Address): Promise<Membership[]> {
    // const memberships: Membership[] = [];

    // const proofsMemberships = await this.getProofsMemberships(walletAddress);

    // if (proofsMemberships.length > 0) {
    //   memberships.push(...proofsMemberships);
    // }

    const nftMemberships = await this.getNftMemberships(walletAddress);

    return nftMemberships;
  }

  // private getProofsMemberships(walletAddress: Address) {
  //   const activeRounds = this.rounds.filter(
  //     (round) => round.state !== PresaleRoundState.vesting,
  //   );

  //   const activeRoundIds = activeRounds.map((round) => round.roundId);

  //   // for each round, get the membership
  //   // const promises = activeRoundIds.map((roundId) => {
  //   //   return presaleContract.read.roundParticipants([BigInt(roundId), walletAddress]);
  //   // });

  //   return Promise.resolve([]);
  // }

  private async getNftMemberships(walletAddress: Address) {
    const memberships: Membership[] = [];

    const membershipNftContract = getContract({
      address: this.membership,
      abi: membershipV2Abi,
      client: this.client,
    });

    const membershipBalance = await membershipNftContract.read.balanceOf([walletAddress]);
    // const membershipBalance = await Promise.resolve(3n);

    if (membershipBalance > BigInt(0)) {
      const allMembershipNfts = Array.from(
        { length: Number(membershipBalance) },
        (_, i) => i,
      );

      // for each membership nft, get the membershipId
      const promises = allMembershipNfts.map((_, index) => {
        return membershipNftContract.read.tokenOfOwnerByIndex([
          walletAddress,
          BigInt(index),
        ]);
      });

      const membershipIds = await Promise.all(promises);

      // for each membershipId, get the membership
      const membershipPromises = membershipIds.map((membershipId) => {
        return Promise.all([
          membershipNftContract.read.getUsage([membershipId]),
          membershipNftContract.read.getAttributes([membershipId]),
          membershipNftContract.read.getRoundId([membershipId]),
          membershipNftContract.read.getStart(),
          membershipNftContract.read.unlocked([membershipId]) as Promise<bigint>,
        ]);
      });

      const membershipData = await Promise.all(membershipPromises);

      memberships.push(
        // @ts-expect-error TODO
        ...membershipData.map((membership, index) => {
          const [usage, attributes, roundId, start, unlocked] = membership;

          return {
            id: membershipIds[index].toString(),
            roundId: Number(roundId),
            usage: {
              max: usage.max.toString(),
              current: usage.current.toString(),
            },
            price: attributes.price.toString(),
            allocation: attributes.allocation.toString(),
            claimableBackUnit: attributes.claimableBackUnit.toString(),
            tgeNumerator: Number(attributes.tgeNumerator),
            tgeDenominator: Number(attributes.tgeDenominator),
            cliffDuration: Number(attributes.cliffDuration),
            cliffNumerator: Number(attributes.cliffNumerator),
            cliffDenominator: Number(attributes.cliffDenominator),
            vestingPeriodCount: Number(attributes.vestingPeriodCount),
            vestingPeriodDuration: Number(attributes.vestingPeriodDuration),
            start: Number(start),
            locked: (usage.max - unlocked).toString(),
            unlocked: unlocked.toString(),
            nextUnlockTimestamp: this.calculateNextUnlock(
              new Date().getTime() / 1000,
              {
                cliffDuration: Number(attributes.cliffDuration),
                vestingPeriodCount: Number(attributes.vestingPeriodCount),
                vestingPeriodDuration: Number(attributes.vestingPeriodDuration),
              },
              Number(start),
            ),
            nextUnlockValue: this.calculateNextUnlockValue(
              new Date().getTime() / 1000,
              {
                usageMax: usage.max.toString(),
                cliffDuration: Number(attributes.cliffDuration),
                vestingPeriodCount: Number(attributes.vestingPeriodCount),
                vestingPeriodDuration: Number(attributes.vestingPeriodDuration),
                tgeNumerator: Number(attributes.tgeNumerator),
                tgeDenominator: Number(attributes.tgeDenominator),
              },
              Number(start),
            ),
          };
        }),
      );
    }

    return memberships;
  }

  public claimTokens(
    walletClient: WalletClient,
    chain: Chain,
    membershipId: string,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.presaleContractAddress,
      abi: presaleV2Abi,
      functionName: 'claim',
      args: [BigInt(membershipId)],
      account: walletClient.account,
      chain,
    });
  }

  public transferMembership(
    walletClient: WalletClient,
    chain: Chain,
    to: Address,
    membershipId: string,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.membership,
      abi: membershipV2Abi,
      functionName: 'transferFrom',
      args: [walletClient.account.address, to, BigInt(membershipId)],
      account: walletClient.account,
      chain,
    });
  }

  private calculateNextUnlockValue(
    timestamp: number,
    attributes: {
      usageMax: string;
      cliffDuration: number;
      vestingPeriodCount: number;
      vestingPeriodDuration: number;
      tgeNumerator: number;
      tgeDenominator: number;
    },
    tgeStartTimestamp: number,
  ) {
    const duration =
      (attributes.vestingPeriodCount - 1) * attributes.vestingPeriodDuration +
      attributes.cliffDuration;

    if (timestamp >= tgeStartTimestamp + duration) return '0';

    const tgeValue =
      (BigInt(attributes.usageMax) * BigInt(attributes.tgeNumerator)) /
      BigInt(attributes.tgeDenominator);

    const unlockValue =
      (BigInt(attributes.usageMax) - tgeValue) / BigInt(attributes.vestingPeriodCount);

    // TGE
    if (timestamp < tgeStartTimestamp) {
      return tgeValue.toString();
    }

    return unlockValue.toString();
  }

  public setApproval(
    walletClient: WalletClient,
    chain: Chain,
    amount: bigint,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.collectedToken,
      abi: presaleV2Abi,
      functionName: 'approve',
      args: [this.presaleContractAddress, amount],
      account: walletClient.account,
      chain,
    });
  }

  public claimBackTokens(
    walletClient: WalletClient,
    chain: Chain,
    membershipId: string,
    amount: bigint,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.presaleContractAddress,
      abi: presaleV2Abi,
      functionName: 'claimback',
      args: [BigInt(membershipId), amount],
      account: walletClient.account,
      chain,
    });
  }

  public buyTokens(
    walletClient: WalletClient,
    chain: Chain,
    membership: Membership,
    amount: bigint,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.presaleContractAddress,
      abi: presaleV2Abi,
      functionName: 'buy',
      args: [BigInt(membership.roundId), amount],
      account: walletClient.account,
      chain,
    });
  }

  public extendAllocation(
    walletClient: WalletClient,
    chain: Chain,
    membershipId: string,
    amount: bigint,
  ): Promise<Address> {
    if (walletClient.account === undefined) {
      throw new Error('Account is undefined');
    }

    return walletClient.writeContract({
      address: this.presaleContractAddress,
      abi: presaleV2Abi,
      functionName: 'extend',
      args: [BigInt(membershipId), amount],
      account: walletClient.account,
      chain,
    });
  }

  private calculateNextUnlock(
    timestamp: number,
    attributes: {
      cliffDuration: number;
      vestingPeriodCount: number;
      vestingPeriodDuration: number;
    },
    tgeStartTimestamp: number,
  ) {
    const duration =
      (attributes.vestingPeriodCount - 1) * attributes.vestingPeriodDuration +
      attributes.cliffDuration;

    const firstUnlock = tgeStartTimestamp + attributes.cliffDuration;

    if (timestamp >= tgeStartTimestamp + duration) return 0;

    // TGE
    if (timestamp < tgeStartTimestamp) {
      return tgeStartTimestamp;
    }

    // First unlock
    if (timestamp < firstUnlock && attributes.cliffDuration != 0) {
      return firstUnlock;
    }

    // calculate next unlock if u know that it is + attributes.vestingPeriodDuration from first unlock
    const periodsSinceStart = Math.floor(
      (timestamp - firstUnlock) / attributes.vestingPeriodDuration,
    );

    return firstUnlock + (periodsSinceStart + 1) * attributes.vestingPeriodDuration;
  }
}
